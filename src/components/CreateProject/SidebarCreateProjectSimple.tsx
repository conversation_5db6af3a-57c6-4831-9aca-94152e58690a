"use client";
import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { FaCircleCheck } from "react-icons/fa6";
import { FaUsers } from "react-icons/fa";
import {
  ComputerDesktopIcon,
  Cog6ToothIcon,
  KeyIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";

import { useCreateProjectStore } from "@/store/createProjectStore";
import { useEditNavigation } from "@/hooks/useEditProject";

const menuSidebarBase = [
  {
    name: "Project information",
    step: "project-information",
    icon: <ComputerDesktopIcon className="w-6 h-6" />,
  },
  {
    name: "Search engines",
    step: "search-engines",
    icon: <Cog6ToothIcon className="w-6 h-6" />,
  },
  {
    name: "Keywords",
    step: "keywords",
    icon: <KeyIcon className="w-6 h-6" />,
  },
  {
    name: "Competitors",
    step: "competitors",
    icon: <FaUsers className="w-6 h-6" />,
  },
  {
    name: "Statistics and Analytics services",
    step: "analytics-services",
    icon: <ChartBarIcon className="w-6 h-6" />,
  },
];

export default function SidebarCreateProjectSimple() {
  const path = usePathname();

  // Store hooks
  const {
    projectInfo,
    searchEngineConfigs,
    keywords,
    competitors,
    canAccessStep,
    isStepComplete,
  } = useCreateProjectStore();

  // Edit navigation hook to get URLs with project_id parameter
  const { urls, buildUrl } = useEditNavigation();

  // Build menu items with proper URLs (including project_id in edit mode)
  const menuSidebar = menuSidebarBase.map((item) => {
    let url: string;
    switch (item.step) {
      case "project-information":
        url = urls.projectInformation;
        break;
      case "search-engines":
        url = urls.searchEngines;
        break;
      case "keywords":
        url = urls.keywords;
        break;
      case "competitors":
        url = urls.competitors;
        break;
      case "analytics-services":
        url = urls.analyticsServices;
        break;
      default:
        url = buildUrl(`/create-project/${item.step}`);
    }

    return {
      ...item,
      url,
    };
  });

  // Helper function to check if a step is accessible
  const isStepAccessible = (step: string): boolean => {
    const stepUrl = `/create-project/${step}`;
    return canAccessStep(stepUrl);
  };

  // Helper function to check if a step is completed
  const isStepCompleted = (step: string): boolean => {
    switch (step) {
      case "project-information":
        return isStepComplete("projectInformation") || !!projectInfo;
      case "search-engines":
        return (
          isStepComplete("searchEngines") || searchEngineConfigs.length > 0
        );
      case "keywords":
        return isStepComplete("keywords") || keywords.length > 0;
      case "competitors":
        return isStepComplete("competitors") || competitors.length > 0;
      case "analytics-services":
        return isStepComplete("analyticsServices");
      default:
        return false;
    }
  };

  const currentStepIndex = menuSidebar.findIndex((item) => 
    path === item.url || path.includes(`/create-project/${item.step}`)
  );
  const progressPercentage = currentStepIndex >= 0 
    ? Math.round(((currentStepIndex + 1) / menuSidebar.length) * 100)
    : 0;

  return (
    <div className="bg-white rounded-2xl border border-gray-100 p-6 shadow-sm">
      {/* Logo Section */}
      <div className="flex justify-center mb-6">
        <Link
          href={"/"}
          className="w-[120px] h-[48px] lg:w-[140px] lg:h-[55px] relative inline-block hover:opacity-80 transition-opacity"
        >
          <Image
            src="/images/appLogo.svg"
            alt="seo analyser logo"
            fill
            quality={100}
            className="w-full h-full object-contain"
            priority
          />
        </Link>
      </div>

      {/* Project Name Section */}
      <div className="mb-6 text-center">
        {projectInfo?.name ? (
          <>
            <h2 className="text-base font-semibold text-[#344054] leading-tight">
              {projectInfo.name}
            </h2>
            <p className="text-sm text-[#344054] mt-1">Project Setup</p>
          </>
        ) : (
          <>
            <h2 className="text-base font-semibold text-[#344054] leading-tight">
              Create Project
            </h2>
            <p className="text-sm text-[#344054] mt-1">Project Setup</p>
          </>
        )}
      </div>

      {/* Navigation Steps */}
      <nav>
        <ul className="space-y-2">
          {menuSidebar.map((item) => {
            const isCompleted = isStepCompleted(item.step);
            const isActive =
              path === item.url ||
              path.includes(`/create-project/${item.step}`);
            const isAccessible = isStepAccessible(item.step);
            const isDisabled = !isAccessible;

            return (
              <li key={item.step}>
                {isDisabled ? (
                  <div className="flex items-center gap-3 px-4 py-3 rounded-xl text-gray-400 cursor-not-allowed">
                    <div className="flex-shrink-0">{item.icon}</div>
                    <span className="text-sm font-medium flex-1">{item.name}</span>
                    <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                ) : (
                  <Link
                    href={item.url}
                    className={`
                      flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-200
                      ${isActive
                        ? "bg-primary/10 text-primary shadow-sm border border-primary/20"
                        : isCompleted
                          ? "text-primary hover:bg-primary/5"
                          : "text-[#344054] hover:bg-gray-50"
                      }
                    `}
                  >
                    <div className="flex-shrink-0">{item.icon}</div>
                    <span className="text-sm font-medium flex-1">{item.name}</span>
                    {isCompleted && (
                      <FaCircleCheck className="w-5 h-5 text-green-500" />
                    )}
                  </Link>
                )}
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Progress Indicator */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs font-medium text-[#344054]">Progress</span>
          <span className="text-xs font-semibold text-primary">
            {progressPercentage}%
          </span>
        </div>
        <div className="w-full bg-gray-100 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-primary to-primary/80 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>
    </div>
  );
}
