@import "tailwindcss";
@import "tw-animate-css";
@import "react-day-picker/style.css";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: var(--font-nunito-sans);
  --color-primary: var(--color-primary);
  --color-secondary: var(--color-secondary);
  --color-background: var(--background);
  --color-light-gray: var(--color-light-gray);
  --color-light-gray-2: var(--color-light-gray-2);
  --color-light-gray-3: var(--color-light-gray-3);
  --color-light-gray-4: var(--color-light-gray-4);
  --color-light-gray-5: var(--color-light-gray-5);
  --color-light-gray-6: var(--color-light-gray-6);
  --color-primary-gray: var(--color-primary-gray);
  --color-light-blue: var(--color-light-blue);
  --color-primary-yellow: var(--color-primary-yellow);
  --color-primary-red: var(--color-primary-red);
  --color-primary-red-2: var(--color-primary-red-2);
  --color-primary-green: var(--color-primary-green);
  --color-primary-green-2: var(--color-primary-green-2);
  --color-primary-orange: var(--color-primary-orange);
  --color-primary-pink: var(--color-primary-pink);
  --color-blue-239: var(--color-blue-239);
  --color-border-input: var(--color-border-input);
  --color-pagination-gray: #344054cc;
  --color-pagination-border: #34405480;
  --color-pink-10: var(--color-pink-10);
  --color-pink-20: var(--color-pink-20);
  --color-positive: var(--color-positive);
  --color-negative: var(--color-negative);
}

/* max-width: 1240px; */
@utility container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1240px;

  @media (max-width: 64rem) {
    padding-inline: 16px;
  }

  @media (min-width: 64rem) and (max-width: 1300px) {
    padding-inline: 40px;
  }

  @media (min-width: 1300px) {
    padding-inline: 0;
  }
}

:root {
  --color-primary: rgba(145, 74, 196, 1);
  --color-secondary: rgba(32, 44, 64, 1);
  --color-light-gray: rgba(164, 164, 164, 1);
  --color-light-gray-2: rgba(174, 174, 174, 1);
  --color-light-gray-3: rgba(68, 77, 85, 1);
  --color-light-gray-4: rgba(115, 115, 115, 1);
  --color-light-gray-5: rgba(145, 145, 145, 1);
  --color-light-gray-6: rgba(184, 184, 184, 1);
  --color-primary-gray: rgba(55, 62, 68, 1);
  --color-light-blue: rgba(152, 182, 195, 1);
  --color-primary-yellow: rgba(195, 144, 0, 1);
  --color-primary-red: rgba(195, 9, 0, 1);
  --color-primary-red-2: rgba(186, 50, 52, 1);
  --color-primary-green: rgba(2, 139, 29, 1);
  --color-primary-green-2: rgba(17, 116, 59, 1);
  --color-positive: rgba(0, 99, 17, 1);
  --color-negative: rgba(195, 9, 0, 1);
  --color-primary-orange: rgba(195, 108, 0, 1);
  --color-primary-pink: rgba(195, 105, 158, 1);
  --color-primary-blue: rgba(145, 74, 196, 0.102);
  --color-blue-239: rgba(66, 102, 179, 1);
  --color-border-input: rgba(32, 44, 64, 0.4);
  --color-pink-10: rgba(145, 74, 196, 0.102);
  --color-pink-20: rgba(145, 74, 196, 0.2);
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: var(--color-primary);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: var(--color-positive);
  --chart-2: var(--color-negative);
  --chart-3: var(--color-primary);
  --chart-4: var(--color-primary-yellow);
  --chart-5: var(--color-light-blue);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

@layer components {
  .max-w-full__customeLG {
    @apply lg:!max-w-full min-[1300px]:!px-10;
  }

  /* Custom max-width utility for 8xl (1408px) */
  .max-w-8xl {
    max-width: 88rem; /* 1408px */
  }

  /* Pagination border color with 80% opacity */
  .border-pagination {
    border-color: var(--color-pagination-border);
  }

  .text-pagination {
    color: var(--color-pagination-border);
  }

  /* Positive/Negative utility classes */
  .text-positive {
    color: var(--color-positive);
  }

  .text-negative {
    color: var(--color-negative);
  }

  .bg-positive {
    background-color: var(--color-positive);
  }

  .bg-negative {
    background-color: var(--color-negative);
  }

  .border-positive {
    border-color: var(--color-positive);
  }

  .border-negative {
    border-color: var(--color-negative);
  }

  .bg-positive\/10 {
    background-color: rgba(0, 99, 17, 0.1);
  }

  .bg-positive\/15 {
    background-color: rgba(0, 99, 17, 0.15);
  }

  .bg-positive\/20 {
    background-color: rgba(0, 99, 17, 0.2);
  }

  .bg-negative\/10 {
    background-color: rgba(195, 9, 0, 0.1);
  }

  .bg-negative\/15 {
    background-color: rgba(195, 9, 0, 0.15);
  }

  .bg-negative\/20 {
    background-color: rgba(195, 9, 0, 0.2);
  }

  .btn {
    @apply rounded-lg py-3 px-6 border flex items-center justify-center gap-[9px] cursor-pointer relative overflow-hidden;
  }

  .btn--sm {
    @apply !px-4 !py-2;
  }

  .btn--primary {
    @apply bg-primary focus:bg-primary/70 border-primary text-white font-bold !leading-[21px] text-base hover:opacity-80 duration-200;
  }

  .btn--outline-light {
    @apply !py-2 !text-sm !border-light-gray !text-secondary/70;
  }

  .btn--outline {
    @apply border-secondary text-secondary font-bold !leading-[21px] text-base;
  }

  .btn--primary__outline_hover {
    @apply bg-white border-primary text-primary font-bold !leading-[21px] text-base focus:text-white focus:bg-primary hover:text-white hover:bg-primary duration-300 transition-all;
  }

  .btn--primary__outline {
    @apply bg-primary/10 border-primary text-primary font-bold !leading-[21px] text-base;
    transition-duration: 700ms;
  }

  .btn--primary__outline::before {
    content: "";
    @apply absolute inset-0 w-0 h-full bg-gradient-to-r from-primary to-primary opacity-10;
    transition-duration: 700ms;
  }

  .btn--primary__outline * {
    @apply z-10;
  }

  .btn--primary__outline:hover {
    @apply text-white;
  }

  .btn--primary__outline:hover::before {
    @apply w-full opacity-100;
  }

  .btn--secondary {
    @apply text-secondary bg-secondary/10 border-transparent text-sm !leading-[19px] font-black;
  }

  .badge {
    @apply p-2 rounded-lg flex items-center justify-center border border-light-gray text-secondary/80 text-xs font-medium;
  }

  .badge--primary {
    @apply border-primary text-primary bg-primary/10;
  }

  .badge--danger {
    @apply border-primary-red text-primary-red bg-primary-red/15 font-semibold;
  }

  .badge--warning {
    @apply border-primary-yellow text-[#c7a00e] bg-primary-yellow/20 font-semibold;
  }

  .badge--success {
    @apply border-primary-green text-primary-green bg-primary-green/15 font-semibold;
  }

  .badge--positive {
    @apply border-positive text-positive bg-positive/15 font-semibold;
  }

  .badge--negative {
    @apply border-negative text-negative bg-negative/15 font-semibold;
  }

  /* Recommendation badges */
  .recommendation-badge {
    @apply min-w-[70px] sm:min-w-[90px] md:min-w-[100px] text-center py-1 px-2 whitespace-nowrap text-xs font-semibold transition-all duration-100 ease-in-out;
  }

  .recommendation-category {
    @apply bg-gray-100 text-gray-700;
  }

  .recommendation-category.active {
    @apply bg-primary/15 text-primary;
  }

  /* Improved recommendation section styling */
  .recommendations-container {
    @apply shadow-md rounded-lg overflow-hidden;
  }

  .recommendation-item {
    @apply transition-all duration-300 hover:shadow-md;
  }

  table thead tr th {
    @apply text-xs font-semibold text-secondary text-left break-words;
  }

  table thead tr th:first-child {
    @apply pl-2 sm:pl-4;
  }

  table tbody tr td {
    @apply text-sm text-secondary text-left first:pl-2 sm:first:pl-4 overflow-hidden last:pr-2 sm:last:pr-4 first:rounded-l-lg last:rounded-r-lg first:border-l last:border-r border-y border-light-gray group-odd:!border-0 break-words;
  }

  .textField__input {
    @apply appearance-auto border outline-0 border-light-gray p-4 rounded-md text-secondary placeholder-light-gray;
    transition: box-shadow 0.2s ease-in-out, border-color 0.2s ease-in-out;
  }

  .textField__input:focus {
    @apply border-light-gray;
    box-shadow: 0 0 0 2px rgba(169, 48, 255, 0.658);
    position: relative;
    z-index: 5;
  }

  /* Ensure icons stay visible above focused inputs */
  .textField__input:focus + .absolute,
  .textField__input:focus ~ .absolute {
    z-index: 10;
  }
}
html {
  scroll-behavior: smooth;
}
html,
body {
  padding: 0;
  margin: 0;
  min-height: 100vh;
  user-select: none;
  background-color: rgba(244, 244, 244, 1) !important;
  /* background-color: var(--color-background) !important; */
}

body {
  overflow-x: hidden;
}

/* Ensure sticky positioning works correctly in create-project layout */
html.create-project-layout,
body.create-project-page {
  overflow-x: hidden;
  overflow-y: auto;
  height: auto;
  min-height: 100vh;
}

/* Force remove scrollbar gutter space for auth and create-project pages */
html.auth-layout,
html.create-project-layout {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

html.auth-layout::-webkit-scrollbar,
html.create-project-layout::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari and Opera */
}

body.auth-page,
body.create-project-page {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

body.auth-page::-webkit-scrollbar,
body.create-project-page::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari and Opera */
}

* {
  user-select: text;
}

button {
  cursor: pointer;
  outline: none;
  user-select: none;
}
span {
  user-select: none;
}
tspan {
  user-select: none;
}
.cursor-pointer {
  user-select: none;
}

.custome-scrollbar::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

.custome-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border: 1px solid var(--color-light-gray);
  border-radius: 4px;
}

.custome-scrollbar::-webkit-scrollbar-thumb {
  background: var(--color-secondary);
  border-radius: 4px;
}

/* **** animations **** */

/* Radix Dropdown Menu animations */
@keyframes slideDownAndFade {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeftAndFade {
  from {
    opacity: 0;
    transform: translateX(2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUpAndFade {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideRightAndFade {
  from {
    opacity: 0;
    transform: translateX(-2px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Animation utility classes for Radix dropdown */
.animate-slideDownAndFade {
  animation: slideDownAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-slideLeftAndFade {
  animation: slideLeftAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-slideUpAndFade {
  animation: slideUpAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-slideRightAndFade {
  animation: slideRightAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes floating {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(25px);
  }
  100% {
    transform: translateY(0);
  }
}

/* Login button pulse animation */
@keyframes login-button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(145, 74, 196, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(145, 74, 196, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(145, 74, 196, 0);
  }
}

/* tailwind animation for pop up  */
.transition-height {
  transition: max-height 0.4s ease, opacity 0.4s ease;
  overflow: hidden;
}

.login-btn.animate-pulse {
  animation: login-button-pulse 2s infinite;
}

.floating-animate {
  animation: floating 7s ease-in-out infinite;
}

@keyframes floating-reverse {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-25px);
  }
  100% {
    transform: translateY(0);
  }
}

.floating-animate-reverse {
  animation: floating-reverse 7s ease-in-out infinite;
}

/* Simple fade-in animation for sidebar */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

@keyframes our-customer-animate-top {
  100% {
    transform: translateX(calc(-322px * 12));
  }
}

@keyframes our-customer-animate-bottom {
  100% {
    transform: translateX(calc(322px * 12));
  }
}

/* **** cutline **** */
.cutline {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.cutline-1 {
  -webkit-line-clamp: 1;
}

.cutline-2 {
  -webkit-line-clamp: 2;
}

.cutline-3 {
  -webkit-line-clamp: 3;
}

.cutline-4 {
  -webkit-line-clamp: 4;
}

/* Tailwind-style line-clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* **** charts **** */
.apexcharts-radar-series line {
  stroke: var(--color-light-gray-2) !important;
  stroke-width: 0.7px !important;
}

.apexcharts-xaxis-label {
  fill: var(--color-light-gray-3) !important;
  color: var(--color-light-gray-3) !important;
  font-size: 8px !important;
  font-weight: 800 !important;
  font-family: var(--font-sans) !important;
}

/* Clickable radar chart labels */
.radar-chart-clickable-label {
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

/* Enhanced clickable labels for radar chart - only apply hover to elements with cursor pointer */
.apexcharts-radar text[style*="cursor: pointer"] {
  transition: all 0.2s ease !important;
}

.apexcharts-radar text[style*="cursor: pointer"]:hover {
  fill: var(--color-primary) !important;
  font-weight: 700 !important;
  transform: scale(1.02) !important;
}

/* Ensure non-clickable elements don't have hover effects */
.apexcharts-radar text:not([style*="cursor: pointer"]) {
  cursor: default !important;
}

/* Remove general hover effects that might interfere */
.apexcharts-xaxis-label tspan {
  transition: all 0.2s ease !important;
}

.recharts-layer .recharts-line-dots *:not(:last-child) {
  display: none;
}

.recharts-cartesian-grid-vertical line:first-child,
.recharts-cartesian-grid-vertical line:last-child {
  stroke: var(--color-light-gray-5) !important;
}

/* **** loading **** */
.loader {
  display: block;
  --height-of-loader: 6px;
  --loader-color: #0071e2;
  width: 150px;
  height: var(--height-of-loader);
  border-radius: 30px;
  background-color: rgba(0, 0, 0, 0.2);
  position: relative;
}

/* Custom animation for analyzing text */
@keyframes analyzing-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Skeleton animation for analyzing text - optimized for performance */
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.analyzing-text {
  /* Base color is a dark gray */
  color: transparent; /* Must be transparent for background-clip to work */

  /* Enhanced gradient overlay for the skeleton effect - darker colors */
  background: linear-gradient(
    90deg,
    rgba(80, 80, 85, 0.9) 0%,
    rgba(60, 60, 65, 0.95) 35%,
    rgb(120, 120, 125) 50%,
    rgba(70, 70, 75, 0.95) 65%,
    rgba(50, 50, 55, 0.9) 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;

  /* Reduced animation speed for better performance */
  animation: skeleton-loading 3s infinite linear;

  /* Typography improvements */
  font-weight: 900;
  letter-spacing: 0.4em; /* Increased letter spacing to match JSX */

  /* Use transform: translateZ(0) to enable GPU acceleration */
  transform: translateZ(0);
  will-change: background-position;
}

/* Animated dots for the analyzing text - ultra-simple approach that always works */
.analyzing-text .dots::after {
  content: "";
  animation: dots-simple 1.2s infinite;
  display: inline-block;
  width: 1em;
  text-align: left;
  margin-left: 0.2em;
  color: rgba(80, 80, 85, 0.95);
  font-weight: 900;
  letter-spacing: 0.1em;
}

@keyframes dots-simple {
  0% {
    content: ".  ";
    opacity: 1; /* First dot - highest opacity */
  }
  33% {
    content: ".. ";
    opacity: 0.7; /* Two dots - medium opacity */
  }
  66% {
    content: "...";
    opacity: 0.4; /* Three dots - lowest opacity */
  }
  100% {
    content: ".  ";
    opacity: 1; /* Back to first dot */
  }
}

/* Fallback for browsers that don't support content animation */
.analyzing-text .dots {
  display: inline-block;
  width: 1em;
  margin-left: 0.2em;
  color: rgba(80, 80, 85, 0.95);
  font-weight: 900;
  animation: dots-opacity 1.2s infinite;
}

@keyframes dots-opacity {
  0%,
  33% {
    opacity: 1; /* First dot - highest opacity */
  }
  34%,
  66% {
    opacity: 0.7; /* Second dot - medium opacity */
  }
  67%,
  100% {
    opacity: 0.4; /* Third dot - lowest opacity */
  }
}

.loader::before {
  content: "";
  position: absolute;
  background: var(--loader-color);
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  border-radius: 30px;
  animation: moving 1s ease-in-out infinite;
}

@keyframes moving {
  50% {
    width: 100%;
  }

  100% {
    width: 0;
    right: 0;
    left: unset;
  }
}

/* Hide scrollbar but keep functionality */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Force no scrollbar gutter - utility class */
.no-scrollbar-gutter {
  scrollbar-gutter: auto !important;
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

.no-scrollbar-gutter::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari and Opera */
}

/* Apply no-scrollbar-gutter to html and body for auth and create-project layouts */
html.auth-layout,
html.create-project-layout,
body.auth-page,
body.create-project-page {
  scrollbar-gutter: auto !important;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

html.auth-layout::-webkit-scrollbar,
html.create-project-layout::-webkit-scrollbar,
body.auth-page::-webkit-scrollbar,
body.create-project-page::-webkit-scrollbar {
  display: none !important;
}

/* Modal scroll lock styles */
.modal-scroll-locked {
  position: fixed !important;
  overflow: hidden !important;
  width: 100% !important;
}

/* Prevent scroll restoration issues */
.modal-scroll-locked html {
  overflow: hidden !important;
}

/* Ensure modal content remains scrollable */
.modal-content-scrollable {
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* Smooth transition for scroll lock */
body {
  transition: none !important; /* Disable transitions during scroll lock to prevent visual glitches */
}

/* Completely disable scrollbar gutter to prevent empty scrollbar space on all pages */
html {
  scrollbar-gutter: auto !important;
}

/* Override any potential scrollbar gutter settings */
html *,
body,
body * {
  scrollbar-gutter: auto !important;
}

/* For auth pages, ensure no scrollbar space is reserved */
html.auth-layout {
  overflow-y: auto !important;
  scrollbar-gutter: auto !important;
}

/* For create-project pages, ensure no scrollbar space is reserved */
html.create-project-layout {
  overflow-y: auto !important;
  scrollbar-gutter: auto !important;
}

/* Ensure auth and create-project body elements don't reserve scrollbar space */
body.auth-page,
body.create-project-page {
  overflow-y: auto !important;
  scrollbar-gutter: auto !important;
}

/* Bright autofill indicator with vibrant purple-pink background */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  box-shadow: 0 0 0px 1000px rgba(255, 165, 218, 0.3) inset !important;
  -webkit-box-shadow: 0 0 0px 1000px rgba(254, 102, 191, 0.3) inset !important;
  -webkit-text-fill-color: var(--color-secondary) !important;
  background-color: rgba(254, 116, 197, 0.3) !important;
  background-image: none !important;
  color: var(--color-secondary) !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* Bright autofill indicator for textField inputs */
.textField__input:-webkit-autofill,
.textField__input:-webkit-autofill:hover,
.textField__input:-webkit-autofill:focus,
.textField__input:-webkit-autofill:active {
  box-shadow: 0 0 0px 1000px rgba(255, 165, 218, 0.3) inset !important;
  -webkit-box-shadow: 0 0 0px 1000px rgba(255, 165, 218, 0.3) inset !important;
  -webkit-text-fill-color: var(--color-secondary) !important;
  background-color: rgba(255, 165, 218, 0.3) !important;
  background-image: none !important;
  color: var(--color-secondary) !important;
  border: 1px solid var(--color-light-gray) !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* Additional autofill prevention for various states */
input:-webkit-autofill::first-line {
  font-family: inherit !important;
  font-size: inherit !important;
  color: var(--color-secondary) !important;
}

/* Prevent autofill from changing placeholder styling */
input:-webkit-autofill::placeholder {
  color: var(--color-light-gray) !important;
  opacity: 1 !important;
}

/* Force consistent styling regardless of autofill state */
input[data-lpignore="true"],
input[data-form-type="other"] {
  background-color: white !important;
  background-image: none !important;
}

/* Badge styles for recommendations */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  text-transform: capitalize;
}

.badge--success {
  background-color: rgba(76, 175, 80, 0.15);
  color: #2e7d32;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.badge--warning {
  background-color: rgba(255, 193, 7, 0.15);
  color: #f57c00;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.badge--danger {
  background-color: rgba(244, 67, 54, 0.15);
  color: #d32f2f;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* A3 page setup */
@page {
  size: A3 portrait;
  margin: 0.5cm;
}

/* PDF container styles */
.pdf-container {
  margin: 0 auto;
  padding: 0;
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 1300px;
  height: auto;
  overflow: visible;
  transform-origin: top center;
}

.pdf-container > div {
  margin-top: 0;
  width: 100%;
}

/* PDF modal content styles */
.pdf-modal-content {
  padding-top: 0 !important;
  height: calc(100% - 60px) !important; /* Adjust for header height */
  display: flex;
  flex-direction: column;
}

/* loading Query styles */

.loader {
  width: 10px;
  aspect-ratio: 1;
  border-radius: 50%;
  animation: l5 1s infinite linear alternate;
}
.loader::before {
  display: none;
}
@keyframes l5 {
  0% {
    box-shadow: 13px 0 #ffffff, -13px 0 rgba(60, 60, 60, 0.133);
    background: #ffffff;
  }

  33% {
    box-shadow: 13px 0 #ffffff, -13px 0 rgba(60, 60, 60, 0.133);
    background: rgba(60, 60, 60, 0.133);
  }

  66% {
    box-shadow: 13px 0 rgba(60, 60, 60, 0.133), -13px 0 #ffffff;
    background: rgba(60, 60, 60, 0.133);
  }

  100% {
    box-shadow: 13px 0 rgba(60, 60, 60, 0.133), -13px 0 #ffffff;
    background: #ffffff;
  }
}

/* Mobile keyboard handling for modals */
@media (max-width: 768px) {
  /* Improve scrolling behavior when keyboard is visible */
  .pdf-modal-content {
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    overscroll-behavior: contain; /* Prevent scroll chaining */
  }

  /* Ensure inputs are visible when focused */
  input:focus,
  textarea:focus {
    position: relative;
    z-index: 2;
  }

  /* Additional styles for when keyboard is visible */
  .pdf-modal-content.keyboard-visible {
    padding-bottom: 20px;
    margin-bottom: 20px;
  }

  /* Adjust form elements for better visibility with keyboard */
  .keyboard-visible input,
  .keyboard-visible textarea {
    font-size: 16px; /* Prevent iOS zoom on focus */
    margin-bottom: 16px; /* Add more space between inputs */
  }

  /* Ensure buttons remain accessible when keyboard is visible */
  .keyboard-visible button[type="submit"],
  .keyboard-visible .btn {
    margin-top: 8px;
    margin-bottom: 20px;
  }
}

/* Print-specific styles for PDF */
@media print {
  /* Hide everything except the PDF when printing */
  body * {
    visibility: hidden;
  }

  .print-pdf,
  .print-pdf * {
    visibility: visible;
  }

  .print-pdf {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  .pdf-container .grid {
    display: grid !important;
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .pdf-container .grid-cols-1 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .pdf-container .overall-scores-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }

  .pdf-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .pdf-container * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Ensure consistent font sizes */
  .pdf-container h1 {
    font-size: 24pt !important;
  }
  .pdf-container h2 {
    font-size: 20pt !important;
  }
  .pdf-container h3 {
    font-size: 16pt !important;
  }
  .pdf-container h4 {
    font-size: 14pt !important;
  }
  .pdf-container h5 {
    font-size: 12pt !important;
  }
  .pdf-container p,
  .pdf-container li,
  .pdf-container td {
    font-size: 10pt !important;
  }

  /* Ensure all backgrounds and colors print */
  .pdf-container .bg-green-500,
  .pdf-container .bg-yellow-500,
  .pdf-container .bg-red-500,
  .pdf-container .bg-primary,
  .pdf-container .bg-green-100,
  .pdf-container .bg-yellow-100,
  .pdf-container .bg-red-100 {
    print-color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
  }

  /* Enhanced PDF page break optimization */
  .print-section {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    margin-bottom: 2rem !important;
    padding: 1.5rem !important;
    /* border: 1px solid #e5e7eb !important; */
    border-radius: 0.75rem !important;
    background: white !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  /* Combined first page section optimization */
  .print-section .first-page-combined {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    min-height: auto !important;
    page-break-after: auto !important;
  }

  .print-section .first-page-combined > div {
    flex-shrink: 0 !important;
  }

  /* Ensure header section is compact in combined layout */
  .first-page-combined .header-section {
    margin-bottom: 0.5rem !important;
  }

  /* Combined screenshot and scores section optimization */
  .print-section .screenshot-scores-combined {
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    min-height: auto !important;
  }

  .print-section .screenshot-scores-combined > div {
    flex-shrink: 0 !important;
  }

  .pdf-section-box {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    margin-bottom: 1.5rem !important;
  }

  .pdf-container .mb-8 {
    page-break-inside: avoid;
  }

  /* Prevent orphaned content */
  .pdf-recommendation-grid {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  /* Recommendations sections should use full width in print */
  .recommendations-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  /* Ensure section titles stay with content */
  .pdf-section-header {
    page-break-after: avoid !important;
    break-after: avoid !important;
  }

  /* Ensure consistent spacing */
  .pdf-container .gap-4 {
    gap: 1rem !important;
  }

  .pdf-container .gap-6 {
    gap: 1.5rem !important;
  }

  /* Fix for images */
  .pdf-container img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    object-fit: contain !important;
    visibility: visible !important;
  }

  /* Force image loading */
  .pdf-container img[loading="lazy"] {
    /* Use attribute selector instead of loading property */
    display: block !important;
    visibility: visible !important;
  }

  /* Ensure Next.js images are visible */
  .pdf-container span[style*="box-sizing: border-box"] {
    display: block !important;
    visibility: visible !important;
  }

  .pdf-container span[style*="box-sizing: border-box"] img {
    object-fit: contain !important;
    visibility: visible !important;
  }

  /* Fix for SVG elements */
  .pdf-container svg {
    display: block !important;
    visibility: visible !important;
  }

  /* Reset scaling for print */
  .pdf-container {
    transform: scale(1) !important;
    max-width: 100% !important;
  }

  /* Ensure modal content is properly displayed */
  .pdf-modal-content {
    height: auto !important;
    overflow: visible !important;
  }

  /* Watermark and logo visibility in print */
  .pdf-container .watermark-container,
  .pdf-container .section-watermark,
  .pdf-container .logo-watermark {
    visibility: visible !important;
    display: flex !important;
    opacity: 1 !important;
  }

  .pdf-container .print-watermark-image {
    visibility: visible !important;
    display: block !important;
    opacity: 0.8 !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Ensure brand logos are clearly visible */
  .pdf-container .section-logo-watermark {
    visibility: visible !important;
    display: flex !important;
    opacity: 1 !important;
  }

  /* Text watermarks should be visible but subtle */
  .pdf-container .section-watermark > div {
    opacity: 0.25 !important;
    color: #9ca3af !important;
  }
}
.mobile-zoom {
  zoom: 0.8;
}

@media (max-width: 768px) {
  .mobile-zoom {
    zoom: 0.5;
  }
}

@media print {
  .mobile-zoom {
    zoom: 0.85 !important;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  /* --color-background: var(--background); */
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  /* --color-primary: var(--primary); */
  --color-primary-foreground: var(--primary-foreground);
  /* --color-secondary: var(--secondary); */
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-chart-positive: var(--color-positive);
  --color-chart-negative: var(--color-negative);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* .dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
} */

.recharts-cartesian-grid-vertical {
  stroke-width: 1px;
  stroke: var(--color-muted);
  opacity: 0.5;
}

.max-w-8xl {
  max-width: 88rem; /* 1408px */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* React Day Picker Custom Variables */
:root {
  --rdp-selected-border: var(--color-primary);
  --rdp-selected-background: rgba(224, 224, 224, 1); /* light gray background */
  --rdp-accent-color: #a947f0 !important;
  accent-color: #914ac4;
}
option:hover {
  background-color: purple !important; /* most browsers ignore this */
  color: white !important;
}
select {
  background-color: #f3e6ff; /* light purple background */
  color: #914ac4; /* dark purple text */
  border: 1px solid #914ac4;
  border-radius: 5px;
  padding: 5px;
}
option {
  background-color: #f9f3fe; /* light purple background */
  color: #7c2fb4; /* dark purple text */
  border: 1px solid #914ac4;
  border-radius: 5px;
  padding: 5px;
}
/* Override react-day-picker selected styles */
.rdp-day_selected {
  border: 1px solid red !important;
  background-color: red !important;
}
.rdrDefinedRangesWrapper {
  display: none !important;
}
.rdrDayToday .rdrDayNumber span:after {
  content: "";
  background: #d7a0ff !important;
  border-radius: 2px;
  width: 18px;
  height: 2px;
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translate(-50%);
}

/* Custom scrollbar styles for project list */
.projects-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.projects-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.projects-scrollbar::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 4px;
}

.projects-scrollbar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

.projects-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.bg-primary-yellow {
  background-color: #f8bd00 !important;
}
